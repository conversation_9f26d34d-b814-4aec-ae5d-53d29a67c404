import React from "react";
import { useSelector } from "react-redux";
import {
  selectStats,
  selectPendingApprovals,
  selectRecentActivity,
  selectUsers,
} from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminDashboard.css";

// Icons
import { FaUsers, FaUserTie, FaVideo, FaDollarSign, FaEye, FaCheck, FaTimes } from "react-icons/fa";
import { MdPendingActions } from "react-icons/md";

const AdminDashboard = () => {
  const stats = useSelector(selectStats);
  const pendingApprovals = useSelector(selectPendingApprovals);
  const recentActivity = useSelector(selectRecentActivity);
  const users = useSelector(selectUsers);

  // Get recent buyers and sellers
  const recentBuyers = users.filter(user => user.role === 'buyer').slice(0, 5);
  const recentSellers = users.filter(user => user.role === 'seller').slice(0, 5);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return 'status-badge active';
      case 'inactive':
        return 'status-badge inactive';
      case 'pending':
        return 'status-badge pending';
      default:
        return 'status-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminDashboard">
        {/* Metrics Cards */}
        <div className="AdminDashboard__metrics">
          <div className="metric-card buyers">
            <div className="metric-icon">
              <FaUsers />
            </div>
            <div className="metric-content">
              <div className="metric-number">{stats.totalBuyers.toLocaleString()}</div>
              <div className="metric-label">Total Buyers</div>
            </div>
          </div>

          <div className="metric-card sellers">
            <div className="metric-icon">
              <FaUserTie />
            </div>
            <div className="metric-content">
              <div className="metric-number">{stats.totalSellers.toLocaleString()}</div>
              <div className="metric-label">Total Sellers</div>
            </div>
          </div>

          <div className="metric-card content">
            <div className="metric-icon">
              <FaVideo />
            </div>
            <div className="metric-content">
              <div className="metric-number">{stats.totalContent.toLocaleString()}</div>
              <div className="metric-label">Total Content</div>
            </div>
          </div>

          <div className="metric-card revenue">
            <div className="metric-icon">
              <FaDollarSign />
            </div>
            <div className="metric-content">
              <div className="metric-number">{formatCurrency(stats.totalRevenue)}</div>
              <div className="metric-label">Total Revenue</div>
            </div>
          </div>
        </div>

        {/* Pending Approvals Section */}
        <div className="AdminDashboard__section">
          <div className="section-header">
            <h2>
              <MdPendingActions className="section-icon" />
              Pending Approvals
              <span className="approval-count">{pendingApprovals.length}</span>
            </h2>
          </div>
          <div className="approvals-list">
            {pendingApprovals.slice(0, 6).map((approval) => (
              <div key={approval.id} className="approval-item">
                <div className="approval-content">
                  <h4>{approval.contentTitle}</h4>
                  <p>by {approval.seller}</p>
                  <span className="approval-category">{approval.category}</span>
                  <span className="approval-date">
                    Submitted: {formatDate(approval.submissionDate)}
                  </span>
                </div>
                <div className="approval-actions">
                  <button className="btn-approve">
                    <FaCheck />
                    Approve
                  </button>
                  <button className="btn-reject">
                    <FaTimes />
                    Reject
                  </button>
                  <button className="btn-view">
                    <FaEye />
                    View
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity Tables */}
        <div className="AdminDashboard__tables">
          {/* Recent Buyers */}
          <div className="AdminDashboard__table-section">
            <div className="table-header">
              <h3>Latest Buyers</h3>
            </div>
            <div className="table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Join Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentBuyers.map((buyer) => (
                    <tr key={buyer.id}>
                      <td>
                        <div className="user-info">
                          <div className="user-avatar">
                            {buyer.profileImage ? (
                              <img src={buyer.profileImage} alt={buyer.firstName} />
                            ) : (
                              <FaUsers />
                            )}
                          </div>
                          <span>{buyer.firstName} {buyer.lastName}</span>
                        </div>
                      </td>
                      <td>{buyer.email}</td>
                      <td>{formatDate(buyer.dateJoined)}</td>
                      <td>
                        <span className={getStatusBadge(buyer.status)}>
                          {buyer.status}
                        </span>
                      </td>
                      <td>
                        <div className="table-actions">
                          <button className="btn-action view">View</button>
                          <button className="btn-action edit">Edit</button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Recent Sellers */}
          <div className="AdminDashboard__table-section">
            <div className="table-header">
              <h3>Latest Sellers</h3>
            </div>
            <div className="table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Join Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentSellers.map((seller) => (
                    <tr key={seller.id}>
                      <td>
                        <div className="user-info">
                          <div className="user-avatar">
                            {seller.profileImage ? (
                              <img src={seller.profileImage} alt={seller.firstName} />
                            ) : (
                              <FaUserTie />
                            )}
                          </div>
                          <span>{seller.firstName} {seller.lastName}</span>
                        </div>
                      </td>
                      <td>{seller.email}</td>
                      <td>{formatDate(seller.dateJoined)}</td>
                      <td>
                        <span className={getStatusBadge(seller.status)}>
                          {seller.status}
                        </span>
                      </td>
                      <td>
                        <div className="table-actions">
                          <button className="btn-action view">View</button>
                          <button className="btn-action edit">Edit</button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
