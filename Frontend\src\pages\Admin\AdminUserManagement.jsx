import React, { useState } from "react";
import { useSelector } from "react-redux";
import { selectUsers } from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminUserManagement.css";

// Icons
import { FaUsers, FaUserTie, FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaToggleOn, FaToggleOff } from "react-icons/fa";
import { MdAdd } from "react-icons/md";

const AdminUserManagement = () => {
  const users = useSelector(selectUsers);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedUsers, setSelectedUsers] = useState([]);

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === "all" || user.role === roleFilter;
    const matchesStatus = statusFilter === "all" || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedUsers(filteredUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // Handle individual select
  const handleSelectUser = (userId) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return 'status-badge active';
      case 'inactive':
        return 'status-badge inactive';
      default:
        return 'status-badge';
    }
  };

  // Get role badge class
  const getRoleBadge = (role) => {
    switch (role) {
      case 'buyer':
        return 'role-badge buyer';
      case 'seller':
        return 'role-badge seller';
      case 'admin':
        return 'role-badge admin';
      default:
        return 'role-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminUserManagement">
        {/* Header Actions */}
        <div className="AdminUserManagement__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
          
          <div className="header-right">
            <button className="btn btn-primary">
              <MdAdd />
              Add New User
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminUserManagement__filters">
          <div className="filter-group">
            <FaFilter className="filter-icon" />
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Roles</option>
              <option value="buyer">Buyers</option>
              <option value="seller">Sellers</option>
              <option value="admin">Admins</option>
            </select>
          </div>

          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          {selectedUsers.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedUsers.length} selected
              </span>
              <button className="btn btn-outline">Activate</button>
              <button className="btn btn-outline">Deactivate</button>
              <button className="btn btn-danger">Delete</button>
            </div>
          )}
        </div>

        {/* Users Table */}
        <div className="AdminUserManagement__table">
          <div className="table-container">
            <table className="users-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                    />
                  </th>
                  <th>User</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Join Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                      />
                    </td>
                    <td>
                      <div className="user-info">
                        <div className="user-avatar">
                          {user.profileImage ? (
                            <img src={user.profileImage} alt={user.firstName} />
                          ) : (
                            user.role === 'seller' ? <FaUserTie /> : <FaUsers />
                          )}
                        </div>
                        <div className="user-details">
                          <span className="user-name">
                            {user.firstName} {user.lastName}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <span className={getRoleBadge(user.role)}>
                        {user.role}
                      </span>
                    </td>
                    <td>
                      <div className="status-toggle">
                        <span className={getStatusBadge(user.status)}>
                          {user.status}
                        </span>
                        <button className="toggle-btn">
                          {user.status === 'active' ? (
                            <FaToggleOn className="toggle-on" />
                          ) : (
                            <FaToggleOff className="toggle-off" />
                          )}
                        </button>
                      </div>
                    </td>
                    <td>{formatDate(user.dateJoined)}</td>
                    <td>
                      <div className="table-actions">
                        <button className="btn-action view" title="View User">
                          <FaEye />
                        </button>
                        <button className="btn-action edit" title="Edit User">
                          <FaEdit />
                        </button>
                        <button className="btn-action delete" title="Delete User">
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="no-results">
              <FaUsers className="no-results-icon" />
              <h3>No users found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="AdminUserManagement__pagination">
          <div className="pagination-info">
            Showing {filteredUsers.length} of {users.length} users
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminUserManagement;
